<?php
// 直接测试配置加载
header('Content-Type: text/html; charset=utf-8');

echo "<h1>UEditorPlus 调试信息</h1>";

// 1. 测试配置文件
echo "<h2>1. 配置文件测试</h2>";
$config_file = __DIR__.'/public/api/ueditorplus/php/config.php';
echo "配置文件路径: " . $config_file . "<br>";
echo "文件是否存在: " . (is_file($config_file) ? '是' : '否') . "<br>";

if (is_file($config_file)) {
    try {
        $config = require $config_file;
        echo "配置加载: 成功<br>";
        echo "配置项数量: " . count($config) . "<br>";
    } catch (Exception $e) {
        echo "配置加载: 失败 - " . $e->getMessage() . "<br>";
    }
} else {
    echo "<span style='color:red'>配置文件不存在！</span><br>";
}

// 2. 测试controller.php
echo "<h2>2. Controller测试</h2>";
$controller_file = __DIR__.'/public/api/ueditorplus/php/controller.php';
echo "Controller文件路径: " . $controller_file . "<br>";
echo "文件是否存在: " . (is_file($controller_file) ? '是' : '否') . "<br>";

// 3. 直接请求测试
echo "<h2>3. 直接请求测试</h2>";
$test_url = 'public/api/ueditorplus/php/controller.php';
echo "<a href='$test_url' target='_blank'>测试默认请求</a><br>";
echo "<a href='$test_url?action=config' target='_blank'>测试config请求</a><br>";

// 4. 模拟请求
echo "<h2>4. 模拟config请求</h2>";
if (is_file($controller_file)) {
    $_GET['action'] = 'config';
    ob_start();
    try {
        include $controller_file;
        $output = ob_get_clean();
        echo "返回内容:<br>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
    } catch (Exception $e) {
        ob_end_clean();
        echo "执行失败: " . $e->getMessage();
    }
}

echo "<h2>5. 检查路径</h2>";
echo "当前目录: " . __DIR__ . "<br>";
echo "API目录: " . __DIR__ . '/public/api/ueditorplus/' . "<br>";
echo "API目录是否存在: " . (is_dir(__DIR__ . '/public/api/ueditorplus/') ? '是' : '否') . "<br>";
?>
