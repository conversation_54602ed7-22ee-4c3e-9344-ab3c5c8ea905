<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>UEditorPlus 简单测试</title>
    <script type="text/javascript" src="public/api/ueditorplus/ueditor.config.js"></script>
    <script type="text/javascript" src="public/api/ueditorplus/ueditor.all.js"></script>
</head>
<body>
    <h1>UEditorPlus 简单测试</h1>
    
    <div>
        <h2>配置测试</h2>
        <button onclick="testConfig()">测试配置加载</button>
        <div id="config-result"></div>
    </div>
    
    <div>
        <h2>编辑器测试</h2>
        <script id="editor" name="content" type="text/plain">
            <p>这里是UEditorPlus编辑器的测试内容</p>
        </script>
    </div>

    <script type="text/javascript">
        // 测试配置加载
        function testConfig() {
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'public/api/ueditorplus/php/controller.php?action=config', true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    var result = document.getElementById('config-result');
                    if (xhr.status === 200) {
                        try {
                            var config = JSON.parse(xhr.responseText);
                            result.innerHTML = '<pre style="background:#f0f0f0;padding:10px;">' + 
                                JSON.stringify(config, null, 2) + '</pre>';
                        } catch(e) {
                            result.innerHTML = '<div style="color:red;">配置解析失败: ' + xhr.responseText + '</div>';
                        }
                    } else {
                        result.innerHTML = '<div style="color:red;">请求失败: ' + xhr.status + '</div>';
                    }
                }
            };
            xhr.send();
        }

        // 初始化编辑器
        var ue = UE.getEditor('editor', {
            serverUrl: 'public/api/ueditorplus/php/controller.php',
            loadConfigFromServer: false,
            initialFrameWidth: '100%',
            initialFrameHeight: 400,
            toolbars: [[
                'fullscreen', 'source', '|',
                'undo', 'redo', '|',
                'bold', 'italic', 'underline', 'strikethrough', '|',
                'forecolor', 'backcolor', '|',
                'insertimage', 'insertvideo', 'attachment'
            ]]
        });

        // 编辑器准备就绪后的回调
        ue.ready(function() {
            console.log('UEditorPlus 编辑器初始化完成');
        });
    </script>
</body>
</html>
