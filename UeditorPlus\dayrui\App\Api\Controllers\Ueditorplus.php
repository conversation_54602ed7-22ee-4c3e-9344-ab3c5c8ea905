<?php namespace Phpcmf\Controllers;

/**
 * UEditorPlus API控制器
 * 兼容XunRuiCMS的API调用方式
 */

class Ueditorplus extends \Phpcmf\Common
{

    public function __construct() {
        parent::__construct();
    }

    /**
     * UEditorPlus后端接口
     */
    public function index() {
        
        // 检查权限
        $error = $this->_check_upload_auth(1);
        if ($error) {
            $this->_json(0, $error);
        }

        // 获取action参数
        $action = \Phpcmf\Service::L('input')->get('action');
        if (!$action) {
            $this->_json(0, '缺少action参数');
        }

        // 加载UEditorPlus配置
        $config_file = ROOTPATH.'api/ueditorplus/php/config.php';
        if (!is_file($config_file)) {
            $this->_json(0, '配置文件不存在');
        }
        
        $CONFIG = require $config_file;
        
        // 设置图片标题标签
        if (isset($CONFIG['imageAltValue']) && $CONFIG['imageAltValue'] == 'name') {
            $CONFIG["imgTitleTag"] = '';
        } else {
            $CONFIG["imgTitleTag"] = defined('UEDITOR_IMG_TITLE') ? UEDITOR_IMG_TITLE : '';
        }

        switch ($action) {
            case 'config':
                $this->_json(1, 'ok', $CONFIG);
                break;

            case 'image':
            case 'uploadimage':
            case 'scrawl':
            case 'uploadscrawl':
            case 'snap':
            case 'uploadsnap':
            case 'video':
            case 'uploadvideo':
            case 'file':
            case 'uploadfile':
                $this->_upload_action($action, $CONFIG);
                break;

            case 'listImage':
            case 'listimage':
            case 'listFile':
            case 'listfile':
            case 'listvideo':
                $this->_list_action($action, $CONFIG);
                break;

            case 'catch':
            case 'catchimage':
                $this->_catch_action($CONFIG);
                break;

            default:
                $this->_json(0, '不支持的操作');
                break;
        }
    }

    /**
     * 上传处理
     */
    private function _upload_action($action, $CONFIG) {
        // 这里可以调用原有的上传逻辑
        // 或者重新实现上传功能
        $result = $this->_include_action_file('action_upload.php', $CONFIG);
        if ($result) {
            echo $result;
        } else {
            $this->_json(0, '上传失败');
        }
    }

    /**
     * 列表处理
     */
    private function _list_action($action, $CONFIG) {
        $result = $this->_include_action_file('action_list.php', $CONFIG);
        if ($result) {
            echo $result;
        } else {
            $this->_json(0, '获取列表失败');
        }
    }

    /**
     * 抓取远程文件
     */
    private function _catch_action($CONFIG) {
        $result = $this->_include_action_file('action_crawler.php', $CONFIG);
        if ($result) {
            echo $result;
        } else {
            $this->_json(0, '抓取失败');
        }
    }

    /**
     * 包含并执行action文件
     */
    private function _include_action_file($filename, $CONFIG) {
        $file_path = ROOTPATH.'api/ueditorplus/php/'.$filename;
        if (is_file($file_path)) {
            // 设置全局变量供action文件使用
            global $CONFIG;
            ob_start();
            $result = include $file_path;
            $output = ob_get_clean();
            return $result ?: $output;
        }
        return false;
    }

    /**
     * 检查上传权限
     */
    private function _check_upload_auth($type = 0) {
        
        // 检查登录状态
        if (!$this->uid) {
            return '请先登录';
        }

        // 检查CSRF token
        if (!\Phpcmf\Service::L('security')->csrf_verify()) {
            return 'CSRF验证失败';
        }

        // 检查上传权限
        if ($type && !$this->_is_admin_auth()) {
            // 检查会员上传权限
            if (!$this->member || !$this->member['is_upload']) {
                return '没有上传权限';
            }
        }

        return '';
    }

    /**
     * 输出JSON
     */
    private function _json($code, $msg, $data = null) {
        $result = [
            'code' => $code,
            'msg' => $msg
        ];
        if ($data !== null) {
            $result['data'] = $data;
        }
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($result, JSON_UNESCAPED_UNICODE);
        exit;
    }
}
