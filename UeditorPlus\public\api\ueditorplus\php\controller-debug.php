<?php
/**
 * UEditorPlus 调试版控制器
 * 用于调试配置加载问题
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: X-Requested-With,X_Requested_With');

// 获取action参数
$action = isset($_GET['action']) ? $_GET['action'] : '';

// 调试信息
$debug = array(
    'action' => $action,
    'get_params' => $_GET,
    'config_file_exists' => is_file(__DIR__.'/config.php'),
    'current_dir' => __DIR__
);

// 如果是config请求，直接返回配置
if ($action === 'config') {
    if (is_file(__DIR__.'/config.php')) {
        $CONFIG = require __DIR__.'/config.php';
        
        // 添加调试信息
        $CONFIG['debug_info'] = $debug;
        
        echo json_encode($CONFIG, JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode(array(
            'state' => '配置文件不存在',
            'debug_info' => $debug
        ), JSON_UNESCAPED_UNICODE);
    }
    exit;
}

// 其他请求
echo json_encode(array(
    'state' => '调试模式 - action: ' . $action,
    'debug_info' => $debug
), JSON_UNESCAPED_UNICODE);
?>
