<?php
/**
 * UEditorPlus 配置测试文件
 * 用于测试后端配置是否能正常加载
 */

header('Content-Type: application/json; charset=utf-8');

// 测试配置文件是否存在
$config_file = __DIR__.'/public/api/ueditorplus/php/config.php';
echo "配置文件路径: " . $config_file . "\n";
echo "配置文件是否存在: " . (is_file($config_file) ? '是' : '否') . "\n";

if (is_file($config_file)) {
    $CONFIG = require $config_file;
    echo "配置加载成功\n";
    echo "配置内容:\n";
    echo json_encode($CONFIG, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
} else {
    echo "配置文件不存在！\n";
}

// 测试controller.php
$controller_file = __DIR__.'/public/api/ueditorplus/php/controller.php';
echo "\n\ncontroller.php文件是否存在: " . (is_file($controller_file) ? '是' : '否') . "\n";

// 测试直接访问config
echo "\n\n测试config请求:\n";
$_GET['action'] = 'config';
ob_start();
include $controller_file;
$output = ob_get_clean();
echo $output;
?>
