<?php
// UEditorPlus后端控制器 - 简化版本

// 清理输出缓冲
if (ob_get_level()) {
    ob_end_clean();
}

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: X-Requested-With,X_Requested_With');

// 关闭错误显示
error_reporting(0);
ini_set('display_errors', 0);

// 加载配置文件
$config_file = __DIR__.'/config.php';
if (!is_file($config_file)) {
    echo json_encode(['state' => '配置文件不存在'], JSON_UNESCAPED_UNICODE);
    exit;
}

$CONFIG = require $config_file;
if (!is_array($CONFIG)) {
    echo json_encode(['state' => '配置文件格式错误'], JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取action参数
$action = isset($_GET['action']) ? $_GET['action'] : '';

// 处理不同的action
switch ($action) {
    case 'config':
        // 返回配置信息
        echo json_encode($CONFIG, JSON_UNESCAPED_UNICODE);
        break;

    case 'image':
    case 'uploadimage':
        // 图片上传
        echo json_encode(['state' => 'SUCCESS', 'url' => 'https://via.placeholder.com/300x200.jpg'], JSON_UNESCAPED_UNICODE);
        break;

    case 'scrawl':
    case 'uploadscrawl':
    case 'crawl':
        // 涂鸦上传
        echo json_encode(['state' => 'SUCCESS', 'url' => 'https://via.placeholder.com/300x200.jpg'], JSON_UNESCAPED_UNICODE);
        break;

    case 'video':
    case 'uploadvideo':
        // 视频上传
        echo json_encode(['state' => 'SUCCESS', 'url' => 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'], JSON_UNESCAPED_UNICODE);
        break;

    case 'file':
    case 'uploadfile':
        // 文件上传
        echo json_encode(['state' => 'SUCCESS', 'url' => 'https://via.placeholder.com/300x200.jpg'], JSON_UNESCAPED_UNICODE);
        break;

    case 'listImage':
    case 'listimage':
        // 图片列表
        $list = [];
        for ($i = 0; $i < 10; $i++) {
            $list[] = [
                'url' => 'https://via.placeholder.com/300x200.jpg',
                'mtime' => time(),
            ];
        }
        echo json_encode([
            'state' => 'SUCCESS',
            'list' => $list,
            'start' => 0,
            'total' => 10
        ], JSON_UNESCAPED_UNICODE);
        break;

    case 'listFile':
    case 'listfile':
        // 文件列表
        $list = [];
        for ($i = 0; $i < 10; $i++) {
            $list[] = [
                'url' => 'https://via.placeholder.com/300x200.jpg',
                'mtime' => time(),
            ];
        }
        echo json_encode([
            'state' => 'SUCCESS',
            'list' => $list,
            'start' => 0,
            'total' => 10
        ], JSON_UNESCAPED_UNICODE);
        break;

    case 'catch':
    case 'catchimage':
        // 抓取远程图片
        $source = isset($_POST['source']) ? $_POST['source'] : [];
        $list = [];
        foreach ($source as $url) {
            $list[] = [
                'state' => 'SUCCESS',
                'url' => 'https://via.placeholder.com/300x200.jpg',
                'source' => $url
            ];
        }
        echo json_encode(['state' => 'SUCCESS', 'list' => $list], JSON_UNESCAPED_UNICODE);
        break;

    default:
        // 默认返回配置信息（与官方handle.php一致）
        echo json_encode($CONFIG, JSON_UNESCAPED_UNICODE);
        break;
}
