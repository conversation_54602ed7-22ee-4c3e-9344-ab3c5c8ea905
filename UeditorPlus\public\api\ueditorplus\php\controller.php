<?php

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: X-Requested-With,X_Requested_With');

// 加载UEditorPlus配置
$config_file = __DIR__.'/config.php';
if (!is_file($config_file)) {
    echo json_encode(array(
        'state'=> '配置文件不存在'
    ), JSON_UNESCAPED_UNICODE);
    exit;
}

$CONFIG = require $config_file;

// 设置图片标题标签
if (isset($CONFIG['imageAltValue']) && $CONFIG['imageAltValue'] == 'name') {
    $CONFIG["imgTitleTag"] = '';
} else {
    $CONFIG["imgTitleTag"] = defined('UEDITOR_IMG_TITLE') ? UEDITOR_IMG_TITLE : '';
}

$action = isset($_GET['action']) ? $_GET['action'] : '';

// 对于config请求，直接返回配置，不需要权限检查
if ($action === 'config') {
    echo json_encode($CONFIG, JSON_UNESCAPED_UNICODE);
    exit;
}

// 其他请求需要基本的权限检查
$error = '';
if (!$action) {
    $error = '缺少action参数';
} elseif (in_array($action, ['image', 'uploadimage', 'scrawl', 'uploadscrawl', 'snap', 'uploadsnap', 'video', 'uploadvideo', 'file', 'uploadfile'])) {
    // 只对上传操作进行token检查
    if (!isset($_GET['token']) || !$_GET['token']) {
        $error = '缺少token参数';
    }
}

if (!$error) {
    // 验证了才能上传
    switch ($action) {
        /* 上传图片 */
        case 'image':
        case 'uploadimage':
            /* 上传涂鸦 */
        case 'scrawl':
        case 'uploadscrawl':
            /* 上传截图 */
        case 'snap':
        case 'uploadsnap':
            /* 上传视频 */
        case 'video':
        case 'uploadvideo':
            /* 上传文件 */
        case 'file':
        case 'uploadfile':
            if (is_file(__DIR__.'/action_upload.php')) {
                $result = include(__DIR__.'/action_upload.php');
            } else {
                $result = json_encode(array('state'=> '上传处理文件不存在'), JSON_UNESCAPED_UNICODE);
            }
            break;

        /* 列出图片 */
        case 'listImage':
        case 'listimage':
        case 'listFile':
        case 'listfile':
        case 'listvideo':
            if (is_file(__DIR__.'/action_list.php')) {
                $result = include(__DIR__.'/action_list.php');
            } else {
                $result = json_encode(array('state'=> '列表处理文件不存在'), JSON_UNESCAPED_UNICODE);
            }
            break;

        /* 抓取远程文件 */
        case 'catch':
        case 'catchimage':
            if (is_file(__DIR__.'/action_crawler.php')) {
                $result = include(__DIR__.'/action_crawler.php');
            } else {
                $result = json_encode(array('state'=> '抓取处理文件不存在'), JSON_UNESCAPED_UNICODE);
            }
            break;

        default:
            $result = json_encode(array(
                'state'=> '不支持的操作: '.$action
            ), JSON_UNESCAPED_UNICODE);
            break;
    }
} else {
    $result = json_encode(array(
        'state'=> $error ? $error : '权限验证失败'
    ), JSON_UNESCAPED_UNICODE);
}

/* 输出结果 */
if (isset($_GET["callback"])) {
    if (preg_match("/^[\w_]+$/", $_GET["callback"])) {
        echo htmlspecialchars($_GET["callback"]) . '(' . $result . ')';
    } else {
        echo json_encode(array(
            'state'=> 'callback参数不合法'
        ), JSON_UNESCAPED_UNICODE);
    }
} else {
    echo $result;
}
exit;
