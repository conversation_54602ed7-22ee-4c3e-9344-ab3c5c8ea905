<?php
// UEditorPlus后端控制器 - 简化版本

// 清理输出缓冲
if (ob_get_level()) {
    ob_end_clean();
}

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: X-Requested-With,X_Requested_With');

// 关闭错误显示
error_reporting(0);
ini_set('display_errors', 0);

// 加载配置文件
$config_file = __DIR__.'/config.php';
if (!is_file($config_file)) {
    echo json_encode(['state' => '配置文件不存在'], JSON_UNESCAPED_UNICODE);
    exit;
}

$CONFIG = require $config_file;
if (!is_array($CONFIG)) {
    echo json_encode(['state' => '配置文件格式错误'], JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取action参数
$action = isset($_GET['action']) ? $_GET['action'] : '';

// 处理不同的action
switch ($action) {
    case 'config':
        // 返回配置信息
        echo json_encode($CONFIG, JSON_UNESCAPED_UNICODE);
        break;

    case 'image':
    case 'uploadimage':
    case 'scrawl':
    case 'uploadscrawl':
    case 'crawl':
    case 'snap':
    case 'uploadsnap':
    case 'video':
    case 'uploadvideo':
    case 'file':
    case 'uploadfile':
        // 所有上传操作
        if (is_file(__DIR__.'/action_upload.php')) {
            include(__DIR__.'/action_upload.php');
        } else {
            echo json_encode(['state' => '上传处理文件不存在'], JSON_UNESCAPED_UNICODE);
        }
        break;

    case 'listImage':
    case 'listimage':
    case 'listFile':
    case 'listfile':
    case 'listvideo':
        // 文件列表操作
        if (is_file(__DIR__.'/action_list.php')) {
            include(__DIR__.'/action_list.php');
        } else {
            echo json_encode(['state' => '列表处理文件不存在'], JSON_UNESCAPED_UNICODE);
        }
        break;

    case 'catch':
    case 'catchimage':
        // 抓取远程图片
        if (is_file(__DIR__.'/action_crawler.php')) {
            include(__DIR__.'/action_crawler.php');
        } else {
            echo json_encode(['state' => '抓取处理文件不存在'], JSON_UNESCAPED_UNICODE);
        }
        break;

    default:
        // 默认返回配置信息（与官方handle.php一致）
        echo json_encode($CONFIG, JSON_UNESCAPED_UNICODE);
        break;
}
