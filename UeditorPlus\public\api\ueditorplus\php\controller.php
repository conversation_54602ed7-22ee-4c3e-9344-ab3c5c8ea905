<?php

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: X-Requested-With,X_Requested_With');

// 加载UEditorPlus配置
$config_file = __DIR__.'/config.php';
if (!is_file($config_file)) {
    echo json_encode(array(
        'state'=> '配置文件不存在: ' . $config_file
    ), JSON_UNESCAPED_UNICODE);
    exit;
}

$CONFIG = require $config_file;

// 设置图片标题标签
if (isset($CONFIG['imageAltValue']) && $CONFIG['imageAltValue'] == 'name') {
    $CONFIG["imgTitleTag"] = '';
} else {
    $CONFIG["imgTitleTag"] = defined('UEDITOR_IMG_TITLE') ? UEDITOR_IMG_TITLE : '';
}

// 统一输出函数
function output($data) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit();
}

$action = isset($_GET['action']) ? $_GET['action'] : '';

// 根据官方实现，采用switch结构处理不同action
switch ($action) {
    case 'config':
        // 返回配置信息
        output($CONFIG);
        break;

    case 'image':
    case 'uploadimage':
        // 图片上传
        if (is_file(__DIR__.'/action_upload.php')) {
            include(__DIR__.'/action_upload.php');
        } else {
            output(['state' => '上传处理文件不存在']);
        }
        break;

    case 'scrawl':
    case 'uploadscrawl':
    case 'crawl':
        // 涂鸦上传
        if (is_file(__DIR__.'/action_upload.php')) {
            include(__DIR__.'/action_upload.php');
        } else {
            output(['state' => '涂鸦上传处理文件不存在']);
        }
        break;

    case 'snap':
    case 'uploadsnap':
        // 截图上传
        if (is_file(__DIR__.'/action_upload.php')) {
            include(__DIR__.'/action_upload.php');
        } else {
            output(['state' => '截图上传处理文件不存在']);
        }
        break;

    case 'video':
    case 'uploadvideo':
        // 视频上传
        if (is_file(__DIR__.'/action_upload.php')) {
            include(__DIR__.'/action_upload.php');
        } else {
            output(['state' => '视频上传处理文件不存在']);
        }
        break;

    case 'audio':
    case 'uploadaudio':
        // 音频上传
        if (is_file(__DIR__.'/action_upload.php')) {
            include(__DIR__.'/action_upload.php');
        } else {
            output(['state' => '音频上传处理文件不存在']);
        }
        break;

    case 'file':
    case 'uploadfile':
        // 文件上传
        if (is_file(__DIR__.'/action_upload.php')) {
            include(__DIR__.'/action_upload.php');
        } else {
            output(['state' => '文件上传处理文件不存在']);
        }
        break;

    case 'listImage':
    case 'listimage':
        // 图片列表
        if (is_file(__DIR__.'/action_list.php')) {
            include(__DIR__.'/action_list.php');
        } else {
            output(['state' => '图片列表处理文件不存在']);
        }
        break;

    case 'listFile':
    case 'listfile':
        // 文件列表
        if (is_file(__DIR__.'/action_list.php')) {
            include(__DIR__.'/action_list.php');
        } else {
            output(['state' => '文件列表处理文件不存在']);
        }
        break;

    case 'listvideo':
        // 视频列表
        if (is_file(__DIR__.'/action_list.php')) {
            include(__DIR__.'/action_list.php');
        } else {
            output(['state' => '视频列表处理文件不存在']);
        }
        break;

    case 'catch':
    case 'catchimage':
        // 抓取远程图片
        if (is_file(__DIR__.'/action_crawler.php')) {
            include(__DIR__.'/action_crawler.php');
        } else {
            output(['state' => '图片抓取处理文件不存在']);
        }
        break;

    default:
        // 默认返回配置信息（按照官方实现）
        output($CONFIG);
        break;
}
