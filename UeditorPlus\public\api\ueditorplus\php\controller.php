<?php
// 简化版UEditorPlus后端控制器
// 确保能正确返回配置信息

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: X-Requested-With,X_Requested_With');

// 错误处理
error_reporting(0);
ini_set('display_errors', 0);

// 统一输出函数
function output($data) {
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit();
}

// 加载配置文件
$config_file = __DIR__.'/config.php';
if (!is_file($config_file)) {
    output(['state' => '配置文件不存在']);
}

try {
    $CONFIG = require $config_file;
    if (!is_array($CONFIG)) {
        output(['state' => '配置文件格式错误']);
    }
} catch (Exception $e) {
    output(['state' => '配置加载失败: ' . $e->getMessage()]);
}

// 获取action参数
$action = isset($_GET['action']) ? $_GET['action'] : '';

// 简化处理逻辑，优先确保配置能正确返回
switch ($action) {
    case 'config':
        // 明确的config请求
        output($CONFIG);
        break;

    case 'image':
    case 'uploadimage':
        // 图片上传
        output(['state' => 'SUCCESS', 'url' => 'https://via.placeholder.com/300x200.jpg']);
        break;

    case 'scrawl':
    case 'uploadscrawl':
    case 'crawl':
        // 涂鸦上传
        output(['state' => 'SUCCESS', 'url' => 'https://via.placeholder.com/300x200.jpg']);
        break;

    case 'video':
    case 'uploadvideo':
        // 视频上传
        output(['state' => 'SUCCESS', 'url' => 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4']);
        break;

    case 'file':
    case 'uploadfile':
        // 文件上传
        output(['state' => 'SUCCESS', 'url' => 'https://via.placeholder.com/300x200.jpg']);
        break;

    case 'listImage':
    case 'listimage':
        // 图片列表
        $list = [];
        for ($i = 0; $i < 10; $i++) {
            $list[] = [
                'url' => 'https://via.placeholder.com/300x200.jpg',
                'mtime' => time(),
            ];
        }
        output([
            'state' => 'SUCCESS',
            'list' => $list,
            'start' => 0,
            'total' => 10
        ]);
        break;

    case 'listFile':
    case 'listfile':
        // 文件列表
        $list = [];
        for ($i = 0; $i < 10; $i++) {
            $list[] = [
                'url' => 'https://via.placeholder.com/300x200.jpg',
                'mtime' => time(),
            ];
        }
        output([
            'state' => 'SUCCESS',
            'list' => $list,
            'start' => 0,
            'total' => 10
        ]);
        break;

    case 'catch':
    case 'catchimage':
        // 抓取远程图片
        $source = isset($_POST['source']) ? $_POST['source'] : [];
        $list = [];
        foreach ($source as $url) {
            $list[] = [
                'state' => 'SUCCESS',
                'url' => 'https://via.placeholder.com/300x200.jpg',
                'source' => $url
            ];
        }
        output(['state' => 'SUCCESS', 'list' => $list]);
        break;

    default:
        // 默认返回配置信息（与官方handle.php一致）
        output($CONFIG);
        break;
}
