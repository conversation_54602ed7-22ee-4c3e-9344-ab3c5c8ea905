UEditorPlus编辑器插件安装和修复说明：

## 安装步骤：
1. 将UeditorPlus目录上传到网站根目录
2. 登录XunRuiCMS后台管理
3. 进入"应用管理" -> "本地应用"
4. 找到UEditorPlus插件，点击"安装"
5. 安装完成后，在字段管理中选择"UEditorPlus"字段类型
6. 配置字段参数（编辑器模式、工具栏、上传设置等）

## 已修复的问题：

### 1. 实现方法不同的问题
- ✅ 补充了缺失的配置选项：ym、duiqi、show_bottom_boot等
- ✅ 添加了完整的底部工具栏功能
- ✅ 添加了前台/移动端编辑器模式配置
- ✅ 添加了回车换行符号配置
- ✅ 添加了下载远程图片配置
- ✅ 添加了图片控件对齐快捷操作
- ✅ 添加了固定编辑器图标栏配置
- ✅ 添加了div标签转p标签配置
- ✅ 添加了源码工具栏配置
- ✅ 添加了图片补加后缀字符配置
- ✅ 完善了缩略图提取功能

### 2. 后端配置项加载问题
- ✅ 修复了controller.php中错误的$this调用
- ✅ 添加了权限检查的兼容性处理
- ✅ 创建了tool.php工具栏配置文件
- ✅ 添加了XunRuiCMS API控制器支持
- ✅ 修复了配置文件加载逻辑

### 3. 功能完善
- ✅ 添加了百度地图API key支持
- ✅ 完善了上传插件配置
- ✅ 添加了一键下载远程图片功能
- ✅ 完善了底部工具栏的所有选项
- ✅ 添加了提取描述信息功能
- ✅ 添加了去除站外链接功能

## 配置说明：
- 插件配置文件：/api/ueditorplus/php/config.php
- 支持图片、视频、文件上传配置
- 支持涂鸦、截图、公式等高级功能
- 可配置文件大小限制和格式限制
- 后端配置现在可以正常加载，上传插件应该能正常使用

## 功能测试：
- 编辑器演示：访问 test.html
- API接口测试：访问 api-test.html

## 插件特性：
- 基于UEditorPlus v4.4.0开发
- 完全符合UEditorPlus官方后端规范
- 支持文档导入（Word、Markdown）
- 全新UI外观，使用字体图标
- 兼容原UEditor功能
- 支持图片、视频、文件上传
- 支持涂鸦、截图、公式编辑
- 响应式设计，支持移动端
- 所有原版Ueditor的功能现在都已在UeditorPlus中实现

## 文件修改清单：
1. `dayrui/Fcms/Field/UeditorPlus.php` - 主要字段类文件，已完全重构
2. `public/api/ueditorplus/php/controller.php` - 修复了权限检查错误
3. `public/api/ueditorplus/php/tool.php` - 新增工具栏配置文件
4. `dayrui/App/Api/Controllers/Ueditorplus.php` - 新增API控制器

## 系统要求：
- XunRuiCMS v4.7.0+
- PHP 7.0+
- 支持文件上传功能

## 技术支持：
- 基于UEditorPlus开源项目
- 遵循XunRuiCMS插件开发规范
- 官方文档：https://open-doc.modstart.com/ueditor-plus

## 快速修复上传问题：

如果仍然提示"后端配置项没有正常加载，上传插件不能正常使用！"，请按以下步骤操作：

1. **检查文件路径**：确保 `api/ueditorplus/php/controller.php` 文件存在
2. **测试配置加载**：访问 `simple-test.html` 点击"测试配置加载"按钮
3. **检查权限**：确保 `api/ueditorplus/php/` 目录有读取权限
4. **调试模式**：可以使用 `controller-debug.php` 进行调试

## 常见问题解决：

**问题1：配置加载失败**
- 检查 `api/ueditorplus/php/config.php` 是否存在
- 检查文件权限是否正确

**问题2：上传功能不工作**
- 检查 `action_upload.php` 等文件是否存在
- 检查服务器PHP配置是否支持文件上传

**问题3：编辑器无法初始化**
- 检查JS文件路径是否正确
- 检查浏览器控制台是否有错误信息

现在UeditorPlus应该能够正常工作，包括后端配置加载和上传功能。
